import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:file_selector/file_selector.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/upload_provider.dart';
import '../../models/upload_file_model.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/upload/upload_zone_widget.dart';
import '../../widgets/upload/upload_stats_widget.dart';
import '../../widgets/upload/upload_file_item_widget.dart';
import '../../widgets/upload/category_selector_widget.dart';

class UploadDocumentScreen extends StatefulWidget {
  final String? preSelectedCategoryId;

  const UploadDocumentScreen({super.key, this.preSelectedCategoryId});

  @override
  State<UploadDocumentScreen> createState() => _UploadDocumentScreenState();
}

class _UploadDocumentScreenState extends State<UploadDocumentScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  String? _selectedCategoryId;

  @override
  void initState() {
    super.initState();
    _selectedCategoryId = widget.preSelectedCategoryId;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: CustomAppBar(
        title: 'Upload File',
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textWhite,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<UploadProvider>(
          builder: (context, uploadProvider, child) {
            return Column(
              children: [
                // Upload Zone Section
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: UploadZoneWidget(
                    onFilesSelected: (files) => _handleFilesSelected(files),
                  ),
                ),

                // Category Selector
                CategorySelectorWidget(
                  selectedCategoryId: _selectedCategoryId,
                  onCategorySelected: (categoryId) {
                    setState(() {
                      _selectedCategoryId = categoryId;
                    });
                  },
                ),

                // Upload Statistics
                if (uploadProvider.totalFiles > 0)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: UploadStatsWidget(
                      totalFiles: uploadProvider.totalFiles,
                      averageProgress: uploadProvider.averageProgress,
                      completedFiles: uploadProvider.completedFiles,
                      failedFiles: uploadProvider.failedFiles,
                    ),
                  ),

                // File List
                Expanded(
                  child: uploadProvider.uploadQueue.isEmpty
                      ? _buildEmptyState()
                      : _buildFileList(uploadProvider.uploadQueue),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.cloud_upload_outlined,
              size: 40,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'No files selected',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tap the upload zone or + button to add files',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFileList(List<UploadFileModel> files) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0),
      itemCount: files.length,
      itemBuilder: (context, index) {
        final file = files[index];
        return AnimatedContainer(
          duration: Duration(milliseconds: 300 + (index * 50)),
          curve: Curves.easeOutBack,
          child: UploadFileItemWidget(
            file: file,
            onPause: () => _pauseUpload(file.id),
            onCancel: () => _cancelUpload(file.id),
            onRetry: () => _retryUpload(file.id),
          ),
        );
      },
    );
  }

  // Handle file selection
  Future<void> _handleFilesSelected(List<XFile> files) async {
    if (files.isNotEmpty) {
      final uploadProvider = Provider.of<UploadProvider>(
        context,
        listen: false,
      );
      await uploadProvider.addFiles(files, categoryId: _selectedCategoryId);
    }
  }

  // Pause/resume upload
  void _pauseUpload(String fileId) {
    final uploadProvider = Provider.of<UploadProvider>(context, listen: false);
    uploadProvider.pauseUpload(fileId);
  }

  // Cancel upload
  void _cancelUpload(String fileId) {
    final uploadProvider = Provider.of<UploadProvider>(context, listen: false);
    uploadProvider.cancelUpload(fileId);
  }

  // Retry failed upload
  void _retryUpload(String fileId) {
    final uploadProvider = Provider.of<UploadProvider>(context, listen: false);
    uploadProvider.retryUpload(fileId);
  }
}
